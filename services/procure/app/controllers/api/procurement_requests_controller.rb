module Api
  class ProcurementRequestsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # Skip ResourceAuthorization for approve/reject actions and handle manually
    authorize_resources except: [ :approve, :reject ]

    # Manual authorization for approval actions
    before_action :set_procurement_request, only: [ :approve, :reject ]
    before_action :authorize_approval_action, only: [ :approve, :reject ]

    api! "Lists all procurement requests"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all procurement requests.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :procurement_request</code>.
    HTML
    )
    returns code: 200, desc: "List of procurement requests"
    def index
      # @procurement_requests is automatically set by authorize_and_load_collection!
      apply_filters(@procurement_requests) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta, serializer: ProcurementRequestSerializer)
      end
    end

    api! "Retrieves a specific request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches a specific procurement request.
      Requires permission: <code>:read, :procurement_request</code>.
    HTML
    )
    returns code: 200, desc: "Procurement request details"
    def show
      serialize_response(@procurement_request, serializer: ProcurementRequestSerializer)
    end

    api! "Creates a new procurement request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :procurement_request, Hash, required: true, desc: "Request details" do
      param :item_id, String, required: true, desc: "ID of the item being requested"
      param :quantity, Integer, required: true, desc: "Quantity requested"
      param :note, String, desc: "Optional employee note"
      param :project_id, String, desc: "Project ID (required for global users, optional for project users)"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new procurement request.
      Requires permission: <code>:create, :procurement_request</code>.
    HTML
    )
    returns code: 201, desc: "Procurement request created successfully"
    def create
      @procurement_request.assign_attributes(procurement_request_params.merge(
        requester_id: current_user.id,
        submitted_at: Time.current
      ))

      if @procurement_request.save
        # Approval workflow automatically triggered via after_create callback
        serialize_response(@procurement_request, serializer: ProcurementRequestSerializer, status: :created)
      else
        serialize_errors(@procurement_request.errors)
      end
    end

    api! "Cancels a procurement request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :cancellation, Hash, desc: "Cancellation details" do
      param :reason, String, desc: "Optional cancellation reason"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Cancels a procurement request that is still pending approval.
      Only works for requests in 'submitted' or 'under_review' status.
      Requires permission: <code>:cancel, :procurement_request</code>.
    HTML
    )
    returns code: 200, desc: "Request cancelled successfully"
    def cancel
      unless @procurement_request.can_be_cancelled?
        return serialize_errors({ detail: "Request cannot be cancelled in current status: #{@procurement_request.status}" })
      end

      result = @procurement_request.cancel_request!(
        current_user.id,
        reason: params.dig(:cancellation, :reason)
      )

      if result.success?
        serialize_response(@procurement_request.reload, serializer: ProcurementRequestSerializer)
      else
        serialize_errors({ detail: result.message })
      end
    end

    api! "Approve current step"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :approval_action, Hash, required: false, desc: "Approval action details" do
      param :comment, String, desc: "Optional approval comment"
    end
    def approve
      result = @procurement_request.approval_approve!(
        current_user.id,
        comment: params.dig(:approval_action, :comment)
      )

      if result.success?
        serialize_response(@procurement_request.reload, serializer: ProcurementRequestSerializer)
      else
        serialize_errors({ detail: result.message })
      end
    end

    api! "Reject current step"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    param :approval_action, Hash, required: false, desc: "Rejection action details" do
      param :comment, String, required: false, desc: "Rejection comment"
    end
    def reject
      result = @procurement_request.approval_reject!(
        current_user.id,
        comment: params.dig(:approval_action, :comment)
      )

      if result.success?
        serialize_response(@procurement_request.reload, serializer: ProcurementRequestSerializer)
      else
        serialize_errors({ detail: result.message })
      end
    end

    api! "Mark as processing"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    description <<-HTML
      Marks an approved request as processing.<br>
      Only works for requests with 'approved' status.<br>
      Requires permission: <code>:process, :procurement_request</code>.
    HTML
    def mark_processing
      unless @procurement_request.approved?
        return serialize_errors({ detail: "Request must be approved before marking as processing. Current status: #{@procurement_request.status}" })
      end

      if @procurement_request.mark_as_processing!
        serialize_response(@procurement_request.reload, serializer: ProcurementRequestSerializer)
      else
        serialize_errors({ detail: "Failed to mark as processing" })
      end
    end

    api! "Mark as delivered"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the request"
    description <<-HTML
      Marks a processing request as delivered.<br>
      Only works for requests with 'processing' status.<br>
      Requires permission: <code>:deliver, :procurement_request</code>.
    HTML
    def mark_delivered
      unless @procurement_request.processing?
        return serialize_errors({ detail: "Request must be processing before marking as delivered. Current status: #{@procurement_request.status}" })
      end

      if @procurement_request.mark_as_delivered!
        serialize_response(@procurement_request.reload, serializer: ProcurementRequestSerializer)
      else
        serialize_errors({ detail: "Failed to mark as delivered" })
      end
    end

    private



    def procurement_request_params
      params.require(:procurement_request).permit(:item_id, :quantity, :note, :project_id)
    end



    def filterable_fields
      %w[status project_id requester_id]
    end

    def sortable_fields
      %w[created_at submitted_at status quantity]
    end

    # Manual resource loading for approval actions
    def set_procurement_request
      @procurement_request = ProcurementRequest.find(params[:id])
    end

    # Manual authorization for approval actions
    def authorize_approval_action
      action_name = params[:action].to_sym
      permission = action_name == :approve ? :approve : :reject

      unless can?(permission, :approval_request)
        render_forbidden("You are not allowed to #{action_name} this procurement_request")
        return false
      end
    end
  end
end
