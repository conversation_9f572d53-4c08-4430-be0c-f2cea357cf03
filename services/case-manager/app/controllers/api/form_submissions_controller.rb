module Api
  class FormSubmissionsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources

    api! "Lists form submissions for a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, desc: "Filter by case ID"
    param :form_section_id, Integer, desc: "Filter by form section ID"
    param :status, String, desc: "Filter by status (form_draft, form_in_progress, form_submitted, form_completed)"
    param_group :pagination_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of form submissions.
      Supports filtering by case, form template, and status.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "List of form submissions"

    def index
      apply_filters(@form_submissions) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific form submission"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific form submission by ID.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submission details"

    def show
      serialize_response(@form_submission)
    end

    api! "Creates a new form submission (with optional case creation)"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, desc: "ID of existing case (omit for new case creation)"
    param :form_section_id, Integer, required: true, desc: "ID of the form section"
    param :form_data, Hash, required: true, desc: "Form field data (field_id => value)"
    param :status, String, desc: "Submission status (form_draft, form_in_progress, form_submitted, form_completed)"
    # Case creation parameters (used when case_id is blank and first workflow template)
    param :assigned_user_id, String, desc: "ID of assigned user (for new case)"
    param :case_type, String, desc: "Case type (for new case)"
    param :priority_level, Integer, desc: "Priority level 1-5 (for new case)"
    param :confidentiality_level, Integer, desc: "Confidentiality level 0-2 (for new case)"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new form submission. If case_id is blank and this is the first workflow template,
      creates a new case and form submission together. Beneficiary data from form_data is used
      to populate case beneficiary fields.
      Requires permission: <code>:create, :form_submission</code>.
    HTML
    )
    returns code: 201, desc: "Form submission created successfully"
    error code: 422, desc: "Validation errors"

    def create
      # Check if this is the first form for a new case
      if params[:case_id].blank? && first_form_template?
        # Create case and form submission together
        ActiveRecord::Base.transaction do
          Rails.logger.info "Creating case with params: #{case_creation_params.inspect}"
          # Create case and skip the automatic form submission creation
          Case.skip_callback(:create, :after, :create_initial_form_submissions)
          @case = Case.create!(case_creation_params)
          Case.set_callback(:create, :after, :create_initial_form_submissions)
          Rails.logger.info "Case created successfully: #{@case.inspect}"

          # Create form submission with integer IDs (now that UUIDs are converted)
          @form_submission = FormSubmission.create!(form_submission_params_for_association)
          Rails.logger.info "Form submission created successfully: #{@form_submission.inspect}"

          # Note: create_initial_form_submissions is automatically called by after_create callback
        end

        # Include case data in response
        serialize_response(@form_submission, status: :created, include: [ :case ])
      else
        # Normal flow - case already exists
        @form_submission = FormSubmission.new(create_params)
        @form_submission.created_by = current_user
        @form_submission.updated_by = current_user

        if @form_submission.save
          serialize_response(@form_submission, status: :created)
        else
          serialize_errors(@form_submission.errors)
        end
      end
    rescue ActiveRecord::RecordInvalid => e
      serialize_errors(e.record.errors)
    end

    api :PUT, "/form_submissions/:id", "Updates an existing form submission"
    api :PATCH, "/form_submissions/:id", "Partially updates a form submission"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param :form_submission, Hash, required: true, desc: "Updated form submission fields" do
      param :form_data, Hash, desc: "Form field data (field_id => value)"
      param :status, String, desc: "Submission status (form_draft, form_in_progress, form_submitted, form_completed)"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing form submission's data and status.
      Validates form data against form template requirements.
      Requires permission: <code>:update, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submission updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      @form_submission.updated_by = current_user

      if @form_submission.update(update_params)
        serialize_response(@form_submission.reload)
      else
        serialize_errors(@form_submission.errors)
      end
    end

    api! "Submit form for completion"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Marks a form submission as completed and validates all required fields.
      Updates the case's form completion status.
      Requires permission: <code>:update, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submitted successfully"
    error code: 422, desc: "Form validation errors"

    def submit
      unless @form_submission.can_be_submitted?
        return render json: {
          error: "Form cannot be submitted",
          details: "All required fields must be completed",
          validation_errors: @form_submission.validation_errors
        }, status: :unprocessable_entity
      end

      if @form_submission.submit!
        render json: {
          message: "Form submitted successfully",
          form_submission_id: @form_submission.id,
          status: @form_submission.status,
          completion_percentage: @form_submission.completion_percentage
        }
      else
        serialize_errors(@form_submission.errors)
      end
    end

    api! "Calculate field values"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param :field_ids, Array, desc: "Array of field IDs to calculate"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Calculates values for calculated fields based on current form data.
      Returns updated field values for frontend display.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Calculated field values"

    def calculate_fields
      field_ids = params[:field_ids] || []
      calculated_values = @form_submission.calculate_field_values(field_ids)

      render json: {
        form_submission_id: @form_submission.id,
        calculated_values: calculated_values
      }
    end

    api! "Creates a component submission (for modal dialogs)"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, required: true, desc: "ID of the case"
    param :form_section_id, Integer, required: true, desc: "ID of the list-type form section"
    param :form_data, Hash, required: true, desc: "Component form data"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a component submission for list-type sections (services, follow-ups, etc.).
      Used for modal dialog forms that create sub-items within a case.
      Requires permission: <code>:create, :form_submission</code>.
    HTML
    )
    returns code: 201, desc: "Component submission created successfully"
    error code: 422, desc: "Validation errors"

    def create_component_submission
      case_obj = Case.find(params[:case_id])
      section = FormSection.find(params[:form_section_id])

      # Validate section is list type and has component template
      unless section.list? && section.sub_form_template&.component?
        return serialize_errors({ detail: "Invalid section for component submission" }, :bad_request)
      end

      # Find the component template's main section
      component_template = section.sub_form_template
      component_section = component_template.form_sections.first

      @submission = FormSubmission.new(
        case: case_obj,
        form_section: component_section,
        form_data: params[:form_data] || {},
        created_by_id: current_user.id,
        submission_order: section.submissions_for_case(case_obj).count + 1
      )

      if @submission.save
        serialize_response(@submission, status: :created)
      else
        serialize_errors(@submission.errors)
      end
    end

    api! "Get section submissions (list items)"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "Form submission ID"
    param :section_id, Integer, required: true, desc: "Section ID"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Gets all submissions for a specific section within a case.
      Used for loading list items in list-type sections.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Section submissions retrieved successfully"

    def section_submissions
      section = FormSection.find(params[:section_id])
      case_obj = @form_submission.case

      @submissions = section.submissions_for_case(case_obj)
      serialize_response(@submissions)
    end

    private

    def create_params
      params.require(:form_submission).permit(
        :case_id,
        :form_section_id,
        :status,
        form_data: {}
      )
    end

    def update_params
      params.fetch(:form_submission, {}).permit(
        :status,
        form_data: {}
      )
    end

    def first_form_template?
      section = FormSection.find(params[:form_section_id])
      template = section.form_template
      template.workflow? && template.sequence_order == 1
    end

    def case_creation_params
      # Ensure we have required IDs
      user_id = current_user&.id || params[:assigned_user_id] || "default_user"
      project_id = current_project&.id || 3 # Default to project 3 if no current project

      {
        assigned_user_id: params[:assigned_user_id] || user_id,
        case_type: params[:case_type] || "general",
        priority_level: params[:priority_level] || 2,
        confidentiality_level: params[:confidentiality_level] || 0,
        created_by_id: user_id,
        project_id: project_id,
        started_at: Time.current,
        status: "draft", # Explicitly set status
        # Beneficiary data from form
        beneficiary_name: params.dig(:form_data, :beneficiary_name),
        beneficiary_gender: params.dig(:form_data, :beneficiary_gender),
        beneficiary_age: params.dig(:form_data, :beneficiary_age),
        beneficiary_nationality: params.dig(:form_data, :beneficiary_nationality),
        beneficiary_phone: params.dig(:form_data, :beneficiary_phone),
        beneficiary_id_number: params.dig(:form_data, :beneficiary_id_number),
        beneficiary_date_of_birth: params.dig(:form_data, :beneficiary_date_of_birth),
        beneficiary_city: params.dig(:form_data, :beneficiary_city)
      }
    end

    def form_submission_params
      # Ensure we have required IDs
      user_id = current_user&.id || params[:assigned_user_id] || "default_user"

      {
        case_id: @case&.id, # This will be set after case creation
        form_section_id: params[:form_section_id],
        form_data: params[:form_data] || {},
        created_by_id: user_id,
        status: :form_draft
      }
    end

    def form_submission_params_for_association
      # For creating through association - explicitly set case_id to ensure it's set
      user_id = current_user&.id || params[:assigned_user_id] || 4

      {
        case_id: @case.id,
        form_template_id: params[:form_template_id],
        form_section_id: params[:form_section_id],
        form_data: params[:form_data] || {},
        created_by_id: user_id,
        status: :form_draft
      }
    end
  end
end
